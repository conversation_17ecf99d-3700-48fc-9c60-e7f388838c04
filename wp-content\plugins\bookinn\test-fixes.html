<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookInn Widget Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 150px; font-weight: bold; }
        input, select, button { padding: 8px; margin: 5px; }
        button { background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .debug-output { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>BookInn Widget Fixes Test Page</h1>
    <p>This page tests the three fixes implemented for the BookInn widget:</p>

    <!-- Test 1: Room Availability Check -->
    <div class="test-section">
        <h3>Test 1: Room Availability Date Parameter Fix</h3>
        <p>Test that the room availability check uses the actual form dates instead of default dates.</p>
        
        <div class="form-group">
            <label for="new-booking-checkin">Check-in Date:</label>
            <input type="date" id="new-booking-checkin" value="2024-01-15">
        </div>
        
        <div class="form-group">
            <label for="new-booking-checkout">Check-out Date:</label>
            <input type="date" id="new-booking-checkout" value="2024-01-17">
        </div>
        
        <div class="form-group">
            <label for="new-booking-adults">Adults:</label>
            <select id="new-booking-adults">
                <option value="1">1</option>
                <option value="2" selected>2</option>
                <option value="3">3</option>
                <option value="4">4</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="new-booking-children">Children:</label>
            <select id="new-booking-children">
                <option value="0" selected>0</option>
                <option value="1">1</option>
                <option value="2">2</option>
            </select>
        </div>
        
        <button id="check-room-availability">Check Room Availability</button>
        <select id="new-booking-room-select-v2">
            <option value="">Select a room...</option>
        </select>
        
        <div id="availability-results" style="display: none;"></div>
        
        <div class="debug-output">
            <strong>Expected Behavior:</strong>
            <ul>
                <li>Console should show the actual dates from form inputs (2024-01-15 to 2024-01-17)</li>
                <li>AJAX request should include these specific dates</li>
                <li>Room dropdown should be updated with available rooms</li>
            </ul>
        </div>
    </div>

    <!-- Test 2: Bookings Table Refresh -->
    <div class="test-section">
        <h3>Test 2: Bookings Table Refresh Fix</h3>
        <p>Test that the refresh button properly updates the bookings table.</p>
        
        <button id="bookinn-refresh-bookings">
            <i class="bookinn-icon-refresh"></i> Refresh
        </button>
        
        <div id="bookings">
            <div class="bookinn-bookings-content">
                <div class="bookinn-table-container">
                    <table class="bookinn-table" id="bookinn-bookings-table">
                        <thead>
                            <tr>
                                <th>ID</th><th>Guest</th><th>Room</th><th>Check-in</th><th>Check-out</th><th>Status</th><th>Total</th><th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="bookings-table-body">
                            <tr><td colspan="8">Click refresh to load bookings...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="debug-output">
            <strong>Expected Behavior:</strong>
            <ul>
                <li>Button should show loading state when clicked</li>
                <li>Table should show loading message</li>
                <li>Table should update with fresh booking data</li>
                <li>Success notification should appear</li>
            </ul>
        </div>
    </div>

    <!-- Test 3: Booking Filters -->
    <div class="test-section">
        <h3>Test 3: Booking Filters Fix</h3>
        <p>Test that booking filters properly apply to the results.</p>
        
        <div class="form-group">
            <label for="filter-status">Status Filter:</label>
            <select id="filter-status">
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="cancelled">Cancelled</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="filter-date-from">Date From:</label>
            <input type="date" id="filter-date-from">
        </div>
        
        <div class="form-group">
            <label for="filter-date-to">Date To:</label>
            <input type="date" id="filter-date-to">
        </div>
        
        <div class="form-group">
            <label for="filter-room">Room:</label>
            <select id="filter-room">
                <option value="">All Rooms</option>
                <option value="101">Room 101</option>
                <option value="102">Room 102</option>
                <option value="201">Room 201</option>
            </select>
        </div>
        
        <button onclick="testApplyFilters()">Apply Filters</button>
        <button onclick="testClearFilters()">Clear Filters</button>
        
        <div class="debug-output">
            <strong>Expected Behavior:</strong>
            <ul>
                <li>Console should show filter values being read correctly</li>
                <li>AJAX request should include filter parameters</li>
                <li>Table should update with filtered results</li>
                <li>Success message should show number of filtered results</li>
            </ul>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Mock BookInn Management object for testing
        window.BookInn = {
            ManagementUnified: {
                config: {
                    ajaxUrl: '/wp-admin/admin-ajax.php',
                    nonce: 'test-nonce'
                },
                cache: { bookings: [] },
                
                showNotification: function(message, type) {
                    console.log(`[${type.toUpperCase()}] ${message}`);
                    alert(`${type.toUpperCase()}: ${message}`);
                },
                
                formatDate: function(date) {
                    return date ? new Date(date).toLocaleDateString() : 'N/A';
                },
                
                formatCurrency: function(amount) {
                    return '€' + parseFloat(amount || 0).toLocaleString();
                },
                
                formatStatusLabel: function(status) {
                    const labels = {
                        'pending': 'Pending',
                        'confirmed': 'Confirmed',
                        'cancelled': 'Cancelled'
                    };
                    return labels[status] || status;
                }
            }
        };
        
        function testApplyFilters() {
            if (window.BookInn && window.BookInn.ManagementUnified && window.BookInn.ManagementUnified.applyBookingFilters) {
                window.BookInn.ManagementUnified.applyBookingFilters();
            } else {
                console.log('applyBookingFilters method not found');
            }
        }
        
        function testClearFilters() {
            if (window.BookInn && window.BookInn.ManagementUnified && window.BookInn.ManagementUnified.clearBookingFilters) {
                window.BookInn.ManagementUnified.clearBookingFilters();
            } else {
                console.log('clearBookingFilters method not found');
            }
        }
        
        // Test console logging
        console.log('BookInn Test Page Loaded');
        console.log('Available methods:', Object.keys(window.BookInn.ManagementUnified));
    </script>
</body>
</html>
